// These are the gradients from Syntax UI (https://syntaxui.com/effects/gradients)

export const syntaxUIGradients = [
  // <PERSON>an to Blue gradients
  "linear-gradient(to right, #22d3ee, #0ea5e9, #0284c7)",
  "linear-gradient(to right, #bfdbfe, #a5f3fc)",
  "linear-gradient(to right, #22d3ee, #0ea5e9, #0284c7)",

  // Purple gradients
  "linear-gradient(to right, #e9d5ff, #d8b4fe, #c084fc)",
  "linear-gradient(to right, #c4b5fd, #a78bfa, #8b5cf6)",

  // Blue gradients
  "linear-gradient(to right, #93c5fd, #60a5fa, #3b82f6)",
  "linear-gradient(to right, #93c5fd, #60a5fa, #3b82f6)",

  // Green gradients
  "linear-gradient(to right, #6ee7b7, #34d399, #10b981)",
  "linear-gradient(to right, #d1fae5, #a7f3d0, #6ee7b7)",

  // Red gradient
  "linear-gradient(to right, #fca5a5, #f87171, #ef4444)",

  // Yellow/Orange gradient
  "linear-gradient(to right, #fde68a, #fbbf24, #f59e0b)",

  // Pink gradient
  "linear-gradient(to right, #fbcfe8, #f9a8d4, #f472b6)",

  // Neon radial gradient
  "radial-gradient(circle at bottom left, #ff00ff, #00ffff)",
];
