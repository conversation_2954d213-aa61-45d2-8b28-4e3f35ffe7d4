{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "extends": ["ultracite"], "assist": {"actions": {"source": {"organizeImports": "off", "useSortedAttributes": "off"}}}, "linter": {"rules": {"a11y": {"noLabelWithoutControl": "off", "noRedundantRoles": "off", "noStaticElementInteractions": "off", "noSvgWithoutTitle": "off", "useAriaPropsForRole": "off", "useAriaPropsSupportedByRole": "off", "useFocusableInteractive": "off", "useKeyWithClickEvents": "off", "useMediaCaption": "off", "useSemanticElements": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off", "noForEach": "off", "noUselessFragments": "off", "useOptionalChain": "off", "useSimplifiedLogicExpression": "off"}, "correctness": {"noUnusedFunctionParameters": "off", "noUnusedImports": "off", "noUnusedVariables": "off", "useExhaustiveDependencies": "off", "useHookAtTopLevel": "off", "useJsxKeyInIterable": "off"}, "nursery": {"noAwaitInLoop": "off", "noBitwiseOperators": "off", "noNestedComponentDefinitions": "off", "noNoninteractiveElementInteractions": "off", "noShadow": "off", "noUnknownAtRule": "off", "useIterableCallbackReturn": "off", "useParseIntRadix": "off", "useSortedClasses": "off"}, "performance": {"noImgElement": "off", "noNamespaceImport": "off", "useTopLevelRegex": "off"}, "style": {"noNestedTernary": "off", "noNonNullAssertion": "off", "useAtIndex": "off", "useBlockStatements": "off", "useCollapsedElseIf": "off", "useCollapsedIf": "off", "useConsistentArrayType": "off", "useDefaultSwitchClause": "off", "useFilenamingConvention": "off", "useForOf": "off", "useImportType": "off", "useNodejsImportProtocol": "off", "useShorthandAssign": "off", "useTemplate": "off", "useNumberNamespace": "off"}, "suspicious": {"noArrayIndexKey": "off", "noConsole": "off", "noDocumentCookie": "off", "noEmptyBlockStatements": "off", "noEvolvingTypes": "off", "noExplicitAny": "off", "noGlobalIsNan": "off", "noImplicitAnyLet": "off", "useAwait": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "preserve", "trailingCommas": "es5", "arrowParentheses": "always", "lineWidth": 80, "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "json": {"formatter": {"indentWidth": 2}}}