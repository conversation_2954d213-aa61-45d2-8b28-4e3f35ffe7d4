services:
  db:
    image: postgres:17
    restart: unless-stopped
    environment:
      POSTGRES_USER: opencut
      POSTGRES_PASSWORD: opencutthegoat
      POSTGRES_DB: opencut
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U opencut"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s

  serverless-redis-http:
    image: hiett/serverless-redis-http:latest
    ports:
      - "8079:80"
    environment:
      SRH_MODE: env
      SRH_TOKEN: example_token
      SRH_CONNECTION_STRING: "redis://redis:6379"
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "wget --spider -q http://127.0.0.1:80 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s
  web:
    build:
      context: .
      dockerfile: ./apps/web/Dockerfile
      args:
        - FREESOUND_CLIENT_ID=${FREESOUND_CLIENT_ID}
        - FREESOUND_API_KEY=${FREESOUND_API_KEY}
    restart: unless-stopped
    ports:
      - "3100:3000" # app is running on 3000 so we run this at 3100
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*******************************************/opencut
      - BETTER_AUTH_URL=http://localhost:3000
      - BETTER_AUTH_SECRET=your-production-secret-key-here
      - UPSTASH_REDIS_REST_URL=http://serverless-redis-http:80
      - UPSTASH_REDIS_REST_TOKEN=example_token
      - FREESOUND_CLIENT_ID=${FREESOUND_CLIENT_ID}
      - FREESOUND_API_KEY=${FREESOUND_API_KEY}
      # Transcription (Optional - leave blank to disable auto-captions)
      - CLOUDFLARE_ACCOUNT_ID=${CLOUDFLARE_ACCOUNT_ID}
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME}
      - MODAL_TRANSCRIPTION_URL=${MODAL_TRANSCRIPTION_URL}
    depends_on:
      db:
        condition: service_healthy
      serverless-redis-http:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  postgres_data:

networks:
  default:
    name: opencut-network
