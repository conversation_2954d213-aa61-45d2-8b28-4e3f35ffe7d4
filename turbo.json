{"$schema": "https://turborepo.com/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "env": ["DATABASE_URL", "BETTER_AUTH_SECRET", "UPSTASH_REDIS_REST_URL", "UPSTASH_REDIS_REST_TOKEN", "MARBLE_WORKSPACE_KEY", "FREESOUND_CLIENT_ID", "FREESOUND_API_KEY", "CLOUDFLARE_ACCOUNT_ID", "R2_ACCESS_KEY_ID", "R2_SECRET_ACCESS_KEY", "R2_BUCKET_NAME", "MODAL_TRANSCRIPTION_URL"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"persistent": true, "cache": false}, "lint": {"dependsOn": ["^lint"], "cache": false}, "lint:fix": {"dependsOn": ["^lint:fix"], "cache": false}, "format": {"dependsOn": ["^format"]}}}