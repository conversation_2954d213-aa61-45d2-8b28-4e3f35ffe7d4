# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/apps/web/node_modules

# next.js
/apps/web/.next/
/apps/web/out/

# debug
/apps/web/npm-debug.log*

# env files (can opt-in for committing if needed)
/apps/web/.env*
!/apps/web/.env.example

# typescript
/apps/web/next-env.d.ts

# asdf version management
.tool-versions

node_modules
.cursorignore
.turbo

*.env

# cursor
bun.lockb
apps/transcription/__pycache__
apps/transcription/env