@import "tailwindcss";

/* Custom variant for dark mode */
@custom-variant dark (&:where(.dark, .dark *));

/* Plugins */
@plugin "@tailwindcss/typography";
@plugin "tailwindcss-animate";

:root {
  /* Custom colors - light mode (default) */
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(0 0% 11%);
  --card: hsl(216, 8%, 86%);
  --card-foreground: hsl(0 0% 2%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0 0% 2%);
  --primary: hsl(205, 84%, 47%);
  --primary-foreground: hsl(0 0% 91%);
  --secondary: hsl(216, 13%, 92%);
  --secondary-foreground: hsl(0 0% 2%);
  --muted: hsl(0 0% 85.1%);
  --muted-foreground: hsl(0 0% 50%);
  --accent: hsl(216, 13%, 92%);
  --accent-foreground: hsl(0 0% 2%);
  --destructive: hsl(0, 83%, 50%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(0 0% 83%);
  --input: hsl(0 0% 85.1%);
  --ring: hsl(0, 0%, 55%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar-background: hsl(0 0% 96.1%);
  --sidebar-foreground: hsl(0 0% 2%);
  --sidebar-primary: hsl(0 0% 2%);
  --sidebar-primary-foreground: hsl(0 0% 91%);
  --sidebar-accent: hsl(0 0% 85.1%);
  --sidebar-accent-foreground: hsl(0 0% 2%);
  --sidebar-border: hsl(0 0% 85.1%);
  --sidebar-ring: hsl(0 0% 16.9%);
  --panel-background: hsl(216 13% 92%);
  --panel-accent: hsl(216, 8%, 86%);

  /* Radius base */
  --radius: 1rem;
}
.dark {
  /* Custom colors - dark mode */
  --background: hsl(0 0% 4%);
  --foreground: hsl(0 0% 89%);
  --card: hsl(0 0% 14.9%);
  --card-foreground: hsl(0 0% 98%);
  --popover: hsl(0 0% 14.9%);
  --popover-foreground: hsl(0 0% 98%);
  --primary: hsl(205, 84%, 53%);
  --primary-foreground: hsl(0 0% 9%);
  --secondary: hsl(0 0% 14.9%);
  --secondary-foreground: hsl(0 0% 98%);
  --muted: hsl(0 0% 14.9%);
  --muted-foreground: hsl(0 0% 63.9%);
  --accent: hsl(0 0% 14.9%);
  --accent-foreground: hsl(0 0% 98%);
  --destructive: hsl(0 100% 60%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 17%);
  --input: hsl(0 0% 14.9%);
  --ring: hsl(0 0% 83.1%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar-background: hsl(0 0% 3.9%);
  --sidebar-foreground: hsl(0 0% 98%);
  --sidebar-primary: hsl(0 0% 98%);
  --sidebar-primary-foreground: hsl(0 0% 9%);
  --sidebar-accent: hsl(0 0% 14.9%);
  --sidebar-accent-foreground: hsl(0 0% 98%);
  --sidebar-border: hsl(0 0% 14.9%);
  --sidebar-ring: hsl(0 0% 83.1%);
  --panel-background: hsl(0 0% 11%);
  --panel-accent: hsl(0 0% 15%);
}

@layer base {
  /*
    The default border color has changed to `currentcolor` in Tailwind CSS v4,
    so we've added these compatibility styles to make sure everything still
    looks the same as it did with Tailwind CSS v3.

    If we ever want to remove these styles, we need to add an explicit border
    color utility to any element that depends on these defaults.
  */
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
  /* Other default base styles */
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Prevent back/forward swipe */
    overscroll-behavior-x: contain;
  }
}

@theme inline {
  /* Responsive breakpoints */
  --breakpoint-xs: 30rem;

  /* Typography */
  --font-sans: var(--font-inter), sans-serif;

  /* Font sizes */
  --text-base: 0.95rem;
  --text-base--line-height: calc(1.5 / 0.95);
  --text-xs: 0.8rem;
  --text-xs--line-height: calc(1 / 0.8);

  /* Border radius */
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 8px);

  /* Palette mapped to root design tokens */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Chart colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Sidebar */
  --color-sidebar: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Panel */
  --color-panel: var(--panel-background);
  --color-panel-accent: var(--panel-accent);

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

@utility scrollbar-x-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar:horizontal {
    display: none;
  }
}

@utility scrollbar-y-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar:vertical {
    display: none;
  }
}

@utility scrollbar-thin {
  &::-webkit-scrollbar {
    width: 6px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: var(--muted-foreground);
  }
}
