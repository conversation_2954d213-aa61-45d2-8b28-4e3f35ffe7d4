// These are the gradients from Pattern Craft (https://patterncraft.fun/)

export const patternCraftGradients = [
  // Dreamy Sky Pink Glow
  "radial-gradient(circle at 30% 70%, rgba(173, 216, 230, 0.35), transparent 60%), radial-gradient(circle at 70% 30%, rgba(255, 182, 193, 0.4), transparent 60%)",

  // Soft Warm Pastel Texture
  "radial-gradient(circle at 20% 80%, rgba(255, 182, 153, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 244, 214, 0.5) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(255, 182, 153, 0.1) 0%, transparent 50%), #fff8f0",

  // Warm Soft Coral & Cream
  "radial-gradient(circle at 20% 80%, rgba(255, 160, 146, 0.25) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 244, 228, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(255, 160, 146, 0.15) 0%, transparent 50%), #fef9f7",

  // Soft Green Glow
  "radial-gradient(circle at center, #8FFFB0, transparent), white",

  // Purple Glow Right
  "radial-gradient(circle at top right, rgba(173, 109, 244, 0.5), transparent 70%)",

  // Teal Glow Right
  "radial-gradient(circle at top right, rgba(56, 193, 182, 0.5), transparent 70%)",

  // Warm Orange Glow Right
  "radial-gradient(circle at top right, rgba(255, 140, 60, 0.5), transparent 70%)",

  // Cool Blue Glow Right
  "radial-gradient(circle at top right, rgba(70, 130, 180, 0.5), transparent 70%)",

  // Purple Glow Left
  "radial-gradient(circle at top left, rgba(173, 109, 244, 0.5), transparent 70%)",

  // Pastel Wave
  "linear-gradient(120deg, #d5c5ff 0%, #a7f3d0 50%, #f0f0f0 100%)",
];
